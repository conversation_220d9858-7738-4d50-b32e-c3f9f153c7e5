import 'dart:convert';
import 'dart:typed_data';

import 'package:esc_pos_utils_plus/esc_pos_utils_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/constants/global_var.dart';
import 'package:gls_self_order/core/services/capture_bill_service.dart';
import 'package:gls_self_order/core/services/capture_qr_service.dart';
import 'package:print_bluetooth_thermal/print_bluetooth_thermal.dart';
import 'package:image/image.dart' as img;

class BluetoothPrinterController extends GetxController {
  RxBool isConnected = false.obs;
  RxList devices = [].obs;
  final previewBytes = Rxn<Uint8List>();

  getDeviceList() async {
    bool connectionStatus = await PrintBluetoothThermal.connectionStatus;
    if(!connectionStatus) {
      isConnected.value = false;
      List<BluetoothInfo> listResult = await PrintBluetoothThermal.pairedBluetooths;
      devices.value = [];
      await Future.forEach(listResult, (BluetoothInfo bluetooth) {
        String name = bluetooth.name;
        String mac = bluetooth.macAdress;
        devices.add({
          'name': name,
          'mac': mac
        });
      });
    }
  }

  testData() async {
    previewBytes.value = await captureBillImage();
  }

  connectPrinter(mac) async {
    Get.snackbar('Thông tin', 'Đang kết nối: $mac');
    final bool result = await PrintBluetoothThermal.connect(macPrinterAddress: mac);
    if(result) {
      AppFunction.showSuccess('Kết nối máy in thành công');
      isConnected.value = true;
      GlobalVar.typePrinter = 'bluetooth';
    }
    else {
      AppFunction.showError('Kết nối thất bại');
    }
  }

  printBill(decoded) async {
    bool connectionStatus = await PrintBluetoothThermal.connectionStatus;
    if (connectionStatus) {
      List<int> ticket = await billData(decoded);
      final result = await PrintBluetoothThermal.writeBytes(ticket);
    } else {
      Get.snackbar('Lỗi', 'Máy in chưa được kết nối');
    }
  }

  billData(decoded) async{
    List<int> bytes = [];
    // Using default profile
    final profile = await CapabilityProfile.load();
    final generator = Generator(PaperSize.mm80, profile);
    bytes += generator.reset();

    if (decoded != null) {
      bytes += generator.image(decoded, align: PosAlign.center);
    }

    bytes += generator.feed(1);
    bytes += generator.cut();
    return bytes;
  }

  printQr() async {
    bool connectionStatus = await PrintBluetoothThermal.connectionStatus;
    if (connectionStatus) {
      List<int> ticket = await qrData();
      final result = await PrintBluetoothThermal.writeBytes(ticket);
    } else {
      Get.snackbar('Lỗi', 'Máy in chưa được kết nối');
    }
  }

  qrData() async {
    List<int> bytes = [];
    // Using default profile
    final profile = await CapabilityProfile.load();
    final generator = Generator(PaperSize.mm80, profile);
    bytes += generator.reset();

    final pngBytes = await captureQrImage();
    final decoded = img.decodeImage(pngBytes);
    if (decoded != null) {
      bytes += generator.image(decoded, align: PosAlign.center);
    }

    bytes += generator.feed(1);
    bytes += generator.cut();
    return bytes;
  }
}