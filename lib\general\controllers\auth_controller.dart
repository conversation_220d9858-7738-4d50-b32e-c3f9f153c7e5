import 'dart:convert';

import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/constants/global_var.dart';
import 'package:gls_self_order/core/constants/sme_url.dart';
import 'package:gls_self_order/core/network/dio_client.dart';
import 'package:gls_self_order/general/classes/user_info.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AuthController extends GetxController {
  //variable
  final DioClientSME dioClient = DioClientSME();
  RxBool verified = false.obs;
  var isLoggedIn = false.obs;
  //function
  login(username, password, remember) async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    try {
      final response = await dioClient.post(SmeUrl.login, data: {
        "Username": username,
        "Password": password,
      });
      dynamic data = response.data;

      // Kiểm tra lỗi license trước (code 1403)
      if (data['Code'] == 1403) {
        sharedPreferences.setString('token', data['Result']);
        return {'success': false, 'code': 1403, 'message': data['Message']};
      }

      if (data['Success']) {
        sharedPreferences.setString('token', data['Result']);
        isLoggedIn.value = true;
        if (remember) {
          sharedPreferences.setString(
              'account',
              jsonEncode({
                'username': username,
                'password': password,
              }));
        }
        return true;
      } else {
        AppFunction.showError(data['Message']);
        return false;
      }
    } catch (e) {
      AppFunction.showError('Lỗi không xác định');
      return false;
    }
  }

  getRememberAccount() async {
    dynamic account = {
      'username': '',
      'password': '',
    };
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    String? accountStorage = sharedPreferences.getString('account');
    if (accountStorage != null) {
      dynamic accountJson = jsonDecode(accountStorage);
      account['username'] = accountJson['username'];
      account['password'] = accountJson['password'];
    }
    return account;
  }

  getUserInfo() async {
    try {
      final response = await dioClient.get(
        SmeUrl.userInfo,
      );
      dynamic data = response.data;
      if (data['Success'] &&
          data['Result'] != null &&
          data['Result']['UserInfo'] != null) {
        UserInfo.user = data['Result']['UserInfo'];
        UserInfo.branches = data['Result']['BranchesAssgined'];
        if (UserInfo.branches.isNotEmpty) {
          UserInfo.branchId = UserInfo.branches[0]['BranchId'];
        }
        if (data['Result']['OrgInfoLogin'] != null) {
          final orgInfo = data['Result']['OrgInfoLogin'];
          UserInfo.orgId = orgInfo['OrgId'];
          UserInfo.licenseKey = orgInfo['LicenseKey'] ?? '';

          // Parse ngày bắt đầu và ngày hết hạn
          UserInfo.startDate = DateTime.tryParse(orgInfo['StartDate'] ?? '');
          UserInfo.expiryDate = DateTime.tryParse(orgInfo['ExpiryDate'] ?? '');

          // Tính số ngày còn lại
          if (UserInfo.expiryDate != null) {
            final now = DateTime.now();
            final diff = UserInfo.expiryDate!.difference(now);
            UserInfo.daysLeft = diff.inDays > 0 ? diff.inDays : 0;
          } else {
            UserInfo.daysLeft = 0;
          }
        }

        UserInfo.activeLicense = data['Result']['UserInfo']['ActiveLicense'];
        UserInfo.activeACB = data['Result']['UserInfo']['ActiveACB'];
      } else {}
    } catch (e) {}
  }

  verifyLicense(code) async {
    try {
      final response = await dioClient.get(
        '${SmeUrl.verifyLicense}/$code',
      );
      dynamic data = response.data;
      if (data['Success'] &&
          data['Result'] != null &&
          data['Result']['LicenseStatus'] == 'ACTIVE') {
        verified.value = true;
        GlobalVar.licenseCode = code;
        return true;
      } else {
        AppFunction.showError('Mã kích hoạt không chính xác');
        return false;
      }
    } catch (e) {
      AppFunction.showError('Lỗi hệ thống');
      return false;
    }
  }

  saveLicense(code) async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    sharedPreferences.setString('license_code', code);
  }

  checkLicense() async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    String? code = sharedPreferences.getString('license_code');
    return code;
  }

  getListBranchByLicense(code) async {
    List list = [];
    try {
      final response = await dioClient.get(
        '${SmeUrl.branchByLicense}/$code',
      );
      dynamic data = response.data;
      if (data['Success'] && data['Result'] != null) {
        list = data['Result'];
      }
    } catch (e) {}
    return list;
  }

  removeLicense() async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    sharedPreferences.remove('license_code');
    verified.value = false;
  }

  checkUseBio() async {
    SharedPreferences storage = await SharedPreferences.getInstance();
    bool? useBio = storage.getBool('use_bio');
    if (useBio != null) {
      return true;
    } else {
      return false;
    }
  }

  enableBio() async {
    SharedPreferences storage = await SharedPreferences.getInstance();
    storage.setBool('use_bio', true);
  }

  disableBio() async {
    SharedPreferences storage = await SharedPreferences.getInstance();
    storage.remove('use_bio');
  }

  getDataBio() async {
    SharedPreferences storage = await SharedPreferences.getInstance();
    String? dataBio = storage.getString('data_bio');
    dynamic data = {
      'username': '',
      'password': '',
    };
    if (dataBio != null) {
      data = jsonDecode(dataBio);
    }
    return data;
  }

  saveDataBio(username, password) async {
    SharedPreferences storage = await SharedPreferences.getInstance();
    dynamic data = {
      'username': username,
      'password': password,
    };
    storage.setString('data_bio', jsonEncode(data));
  }

  removeDataBio() async {
    SharedPreferences storage = await SharedPreferences.getInstance();
    storage.remove('data_bio');
  }

  register(licenseKey, orgName, branchName, username, password, confirmPassword,
      fullName, phone, type, businessType, isEnterprise, businessType1) async {
    
    try {
     if(isEnterprise == "HKD"){
      orgName = isEnterprise;
     }
    
      final response = await dioClient.post(SmeUrl.register, data: {
        "LicenseKey": licenseKey == '' ? null : licenseKey,
        "OrgName": orgName,
        "BranchName": branchName,
        "Username": username,
        "Password": password,
        "FullName": fullName,
        "PhoneNumber": phone,
        "BusinessType": businessType.toString(),
        "OrgType": isEnterprise,
        "BusinessType1": businessType == 8 ? businessType1 : "",
      });
      dynamic data = response.data;
      if (data['Success'] && data['Code'] == 200) {
        AppFunction.showSuccess(data['Message']);
        return {'success': true, 'username': username, 'password': password};
      } else {
        AppFunction.showError(data['Message']);
        return {'success': false, 'username': '', 'password': ''};
      }
    } catch (e) {
      AppFunction.showError('Có lỗi trong quá trình đăng ký');
      return {'success': false, 'username': '', 'password': ''};
    }
  }

  changePassword(oldPass, newPass, confirmPass) async {
    if (oldPass == '') {
      AppFunction.showError('Vui lòng nhập mật khẩu cũ');
      return false;
    }
    if (newPass == '') {
      AppFunction.showError('Vui lòng nhập mật khẩu mới');
      return false;
    }
    if (confirmPass == '') {
      AppFunction.showError('Vui lòng xác nhận mật khẩu mới');
      return false;
    }
    if (newPass != confirmPass) {
      AppFunction.showError('Nhập lại mật khẩu không chính xác');
      return false;
    }
    try {
      final response = await dioClient.post(SmeUrl.changePassword, data: {
        "CurrentPassword": oldPass,
        "NewPassword": newPass,
      });
      dynamic data = response.data;
      if (data['Success'] && data['Code'] == 200) {
        AppFunction.showSuccess(data['Message']);
        return true;
      } else {
        AppFunction.showError(data['Message']);
        return false;
      }
    } catch (e) {
      AppFunction.showError('Có lỗi trong quá trình đổi mật khẩu');
      return false;
    }
  }

  postActiveLicense(code) async {
    if (code == '') {
      AppFunction.showError('Vui lòng xác nhập mã khuyến mãi');
      return false;
    }

    try {
      final response = await dioClient.post(
        '${SmeUrl.activeLicense}?licenseKey=$code',
      );
      dynamic data = response.data;
      if (data['Success'] && data['Code'] == 200) {
        AppFunction.showSuccess(data['Message']);
        return true;
      } else {
        AppFunction.showError(data['Message']);
        return false;
      }
    } catch (e) {
      AppFunction.showError('Có lỗi trong quá trình áp dụng mã kích hoạt');
      return false;
    }
  }

  getEinvoiceConfig() async {
    dynamic item;
    try {
      final response = await dioClient.get(
        SmeUrl.invoiceInfo,
      );
      dynamic data = response.data;
      if (data['Success'] && data['Result'] != null) {
        item = data['Result'];
      }
    } catch (e) {}
    return item;
  }

  postSaveEinvoiceConfig(serialNo, pattern, username, password, api, taxCode,
      isSign, isActive, type) async {
    if (api == '') {
      AppFunction.showError('Vui lòng nhập đường dẫn API');
      return false;
    }
    if (username == '') {
      AppFunction.showError('Vui lòng nhập tên đăng nhập');
      return false;
    }
    if (password == '') {
      AppFunction.showError('Vui lòng nhập mật khẩu');
      return false;
    }
    if (taxCode == '') {
      AppFunction.showError('Vui lòng nhập mã số thuế');
      return false;
    }
    if (serialNo == '') {
      AppFunction.showError('Vui lòng nhập mẫu số');
      return false;
    }
    if (pattern == '') {
      AppFunction.showError('Vui lòng nhập ký hiệu');
      return false;
    }
    dynamic body = {};
    if (type == 'insert') {
      body = {
        "SerialNo": serialNo,
        "Patten": pattern,
        "UserName": username,
        "Password": password,
        "ApiUrl": api,
        "TaxCode": taxCode,
        "MIN_IsSign": isSign
      };
    } else {
      body = {
        "SerialNo": serialNo,
        "Patten": pattern,
        "UserName": username,
        "Password": password,
        "ApiUrl": api,
        "TaxCode": taxCode,
        "MIN_IsSign": isSign,
        "IsActived": isActive
      };
    }
    try {
      final response = await dioClient.post(
          type == 'insert' ? SmeUrl.invoiceInsert : SmeUrl.invoiceUpdate,
          data: body);
      dynamic data = response.data;
      if (data['Success'] && data['Code'] == 200) {
        AppFunction.showSuccess(data['Message']);
        return true;
      } else {
        AppFunction.showError(data['Message']);
        return false;
      }
    } catch (e) {
      AppFunction.showError('Có lỗi trong quá trình lưu cấu hình');
      return false;
    }
  }

  bool get loginStatus => isLoggedIn.value;

  void setLoginStatus(bool status) {
    isLoggedIn.value = status;
  }


}
