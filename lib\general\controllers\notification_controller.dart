import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import '../../sme/presentation/notification/home_notification_manager.dart';
import '../classes/notification_model.dart';

enum NotificationType { all, unread, system, transaction }

class NotificationController extends GetxController {
  final _notifications = <NotificationModel>[].obs;
  final _filteredNotifications = <NotificationModel>[].obs;
  final _currentFilter = NotificationType.all.obs;

  List<NotificationModel> get notifications => _notifications.toList();
  List<NotificationModel> get filteredNotifications => _filteredNotifications.toList();
  NotificationType get currentFilter => _currentFilter.value;

  @override
  void onInit() {
    super.onInit();
    _loadNotifications();
  }

  Future<void> _loadNotifications() async {
    try {
      final storageNotifications = await HomeNotificationManager.getStorageNotifications();

      final newNotifications = storageNotifications.map((data) {
        return NotificationModel.fromJson(data);
      }).toList();

      _notifications.assignAll(newNotifications);
      _applyFilter();

      print('Loaded ${newNotifications.length} notifications from storage');
    } catch (e) {
      print('Error loading notifications: $e');
    }
  }

  void _applyFilter() {
    switch (_currentFilter.value) {
      case NotificationType.all:
        _filteredNotifications.assignAll(_notifications);
        break;
      case NotificationType.unread:
        _filteredNotifications.assignAll(_notifications.where((n) => !n.isRead).toList());
        break;
      case NotificationType.system:
        _filteredNotifications.assignAll(_notifications.where((n) => n.type == 'system').toList());
        break;
      case NotificationType.transaction:
        _filteredNotifications.assignAll(_notifications.where((n) => n.type == 'transaction').toList());
        break;
    }
  }

  void setFilter(NotificationType type) {
    _currentFilter.value = type;
    _applyFilter();
  }

  Future<void> markAsRead(String notificationId) async {
    try {
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        final notification = _notifications[index];
        if (!notification.isRead) {
          final updatedNotification = notification.copyWith(isRead: true);
          _notifications[index] = updatedNotification;
          await HomeNotificationManager.markNotificationAsRead(notificationId);
          _applyFilter();
          print('Marked notification as read: $notificationId');
        }
      }
    } catch (e) {
      print('Error marking notification as read: $e');
    }
  }

  Future<void> clearAllNotifications() async {
    try {
      _notifications.clear();
      _filteredNotifications.clear();
      await HomeNotificationManager.clearStorageNotifications();
      print('Cleared all notifications from controller');
    } catch (e) {
      print('Error clearing all notifications: $e');
    }
  }

  Future<void> removeNotification(String notificationId) async {
    try {
      _notifications.removeWhere((n) => n.id == notificationId);
      _applyFilter();
      print('Removed notification: $notificationId');
    } catch (e) {
      print('Error removing notification: $e');
    }
  }

  void handleNewNotification(RemoteMessage message) {
    try {
      HomeNotificationManager.savePendingNotification(message).then((_) {
        _loadNotifications();
        print('Handled new notification: ${message.notification?.title}');
      });
    } catch (e) {
      print('Error handling new notification: $e');
    }
  }

  Future<void> refreshNotifications() async {
    await _loadNotifications();
  }
}