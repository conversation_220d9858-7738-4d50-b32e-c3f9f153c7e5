import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/components/reusable_filter_widget.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/controllers/customer_controller.dart';
import 'package:gls_self_order/general/controllers/general_controller.dart';
import 'package:gls_self_order/general/controllers/order_controller.dart';
import 'package:gls_self_order/general/views/order/order_detail_view.dart';

import 'package:gls_self_order/sme/domain/usecases/order/generate_acb_usecase.dart';
import 'package:intl/intl.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:socket_io_client/socket_io_client.dart' as IO;
import 'package:image/image.dart' as img;

import '../../../core/constants/api_url.dart';
import '../../../core/constants/global_var.dart';
import '../../../core/constants/sme_url.dart';
import '../../../core/controllers/bluetooth_printer_controller.dart';
import '../../../core/controllers/usb_printer_controller.dart';
import '../../../core/network/dio_client.dart';
import '../../../core/theme/app_style.dart';
import '../../../sme/data/datasources/order/order_remote_data_source.dart';
import '../../../sme/data/repositories/order/order_repository_impl.dart';
import '../../classes/user_info.dart';

class OrdersView extends StatefulWidget {
  const OrdersView({super.key});

  @override
  State<OrdersView> createState() => _OrdersViewState();
}

class _OrdersViewState extends State<OrdersView> {
  //variable
  GeneralController generalController = Get.find();
  OrderController orderController = Get.find();
  CustomerController customerController = Get.find();
  List orders = [];
  Map<String, String> statuses = {};
  List paymentMethods = [];
  String viewType = 'order';
  int totalOrder = 0;
  num totalAmount = 0;
  //for customer
  List customerList = [];

  // Filter data
  late FilterData filterData;

  final BluetoothPrinterController bluetoothPrinterController = Get.find();
  final UsbPrinterController usbPrinterController = Get.find();
  late IO.Socket socket;
  final RxString acbQRCodeData = ''.obs;
  final RxString orderCodeForPayment = ''.obs;
  final RxDouble paymentAmount = 0.0.obs;
  final RxBool paymentSuccess = false.obs;
  final RxString qrError = RxString('');
  final RxBool isLoadingPayment = false.obs;
  final DioClientSME dioClient = DioClientSME();

  //function
  @override
  void initState() {
    super.initState();
    // Initialize statuses map
    statuses = <String, String>{};

    // Initialize filter data
    filterData = FilterData(
      dateFrom: DateTime.now(),
      dateTo: DateTime.now(),
      statusSelected: 'SUCCESS',
      statusName: 'Thành công',
      paymentMethodSelected: '',
      paymentMethodName: 'Tất cả',
      viewType: 'order',
    );
    getData();
    connectSocket();
  }

  // Thêm vào dispose()
  @override
  void dispose() {
    socket.dispose();
    super.dispose();
  }

  // Hàm kết nối socket
  void connectSocket() {
    socket = IO.io(ApiUrl.socketUrl, <String, dynamic>{
      'transports': ['websocket'],
      'autoConnect': false,
    });

    socket.connect();

    socket.onConnect((_) {
      socket.emit('order-group', UserInfo.orgId);
    });

    socket.on('PaymentSuccess', (data) async {
      String code = data['orderCode'] ?? '';
      if (code == orderCodeForPayment.value) {
        paymentSuccess.value = true;
        dynamic decoded = await getImageBill(orderCodeForPayment.value, UserInfo.branchId);
        if (decoded != null) {
          if (GlobalVar.typePrinter == 'bluetooth') {
            bluetoothPrinterController.printBill(decoded);
          }
          else if (GlobalVar.typePrinter == 'usb') {
            usbPrinterController.printBill(decoded);
          }
        }
      }
    });

    socket.onDisconnect((_) {
      print('Socket disconnected');
    });
  }

  // Hàm lấy hình ảnh bill
  Future<dynamic> getImageBill(orderId, brandId) async {
    DioClientSME dioClientSME = DioClientSME();
    try {
      final response = await dioClientSME.post(
        SmeUrl.genBillUrl,
        data: {
          "orderId": orderId,
          "branchId": brandId,
        },
        options: Options(
          responseType: ResponseType.bytes,
          headers: {
            'Accept': 'image/png',
          },
        ),
      );
      Uint8List imageBytes = response.data!;
      final decoded = img.decodeImage(imageBytes);
      return decoded;
    } catch (e) {
      return null;
    }
  }

  getData() async {
    AppFunction.showLoading();
    await getOrderList();
    calSummary();
    await getStatusList();
    await getPaymentMethodList();
    AppFunction.hideLoading();
    setState(() {

    });
  }

  getOrderList() async {
    orders = await orderController.getList(
      DateFormat('yyyy-MM-dd').format(filterData.dateFrom),
      DateFormat('yyyy-MM-dd').format(filterData.dateTo),
      null,
      filterData.paymentMethodSelected.isEmpty ? null : filterData.paymentMethodSelected,
      filterData.statusSelected
    );
  }

  getCustomerList() async {
    customerList = await orderController.getListGroupCustomer(
      DateFormat('yyyy-MM-dd').format(filterData.dateFrom),
      DateFormat('yyyy-MM-dd').format(filterData.dateTo),
      filterData.statusSelected
    );
  }
  
  calSummary() {
    totalOrder = orders.length;
    totalAmount = 0;
    for(dynamic item in orders) {
      totalAmount += num.parse(item['OrderTotalView'].toString());
    }
  }

  calSummaryCustomer() {
    totalOrder = customerList.length;
    totalAmount = 0;
    for(dynamic item in customerList) {
      totalAmount += num.parse(item['SumTotal'].toString());
    }
  }

  getStatusList() async {
    List statusList = await orderController.getStatusList();
    statuses.clear(); // Clear existing data
    for(dynamic item in statusList) {
      statuses[item['StatusCode'].toString()] = item['StatusName'].toString();
    }
  }

  getPaymentMethodList() async {
    paymentMethods = await generalController.getPaymentMethodList();
  }

  // Filter handling methods
  void onFilterChanged(FilterData newFilterData) async {
    AppFunction.showLoading();

    filterData = newFilterData;
    viewType = newFilterData.viewType;

    if (viewType == 'order') {
      await getOrderList();
      calSummary();
    } else {
      await getCustomerList();
      calSummaryCustomer();
    }

    AppFunction.hideLoading();
    setState(() {});
  }

  void onClearFilter() async {
    AppFunction.showLoading();
    filterData = FilterData(
      dateFrom: DateTime.now(),
      dateTo: DateTime.now(),
      statusSelected: 'SUCCESS',
      statusName: 'Thành công',
      paymentMethodSelected: '',
      paymentMethodName: 'Tất cả',
      viewType: viewType,
    );

    if (viewType == 'order') {
      await getOrderList();
      calSummary();
    } else {
      await getCustomerList();
      calSummaryCustomer();
    }

    AppFunction.hideLoading();
    setState(() {});
  }


  


  Widget renderSummary() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        RichText(text: TextSpan(
          children: [
            TextSpan(text: totalOrder.toString(), style: TextStyle(color: Colors.blueAccent, fontSize: 16)),
            TextSpan(text: viewType == 'order' ? ' đơn hàng' : ' khách hàng', style: TextStyle(color: Colors.black, fontSize: 16)),
          ]
        )),
        RichText(text: TextSpan(
            children: [
              TextSpan(text: 'Tổng bán ', style: TextStyle(color: Colors.black, fontSize: 16)),
              TextSpan(text: AppFunction.formatMoney(totalAmount), style: TextStyle(color: Colors.blueAccent, fontSize: 16)),
            ]
        ))
      ],
    );
  }

  Widget renderItemOrder(item) {
    final statusCode = item['StatusCode'] ?? '';
    final statusColor = getStatusColor(statusCode);

    return InkWell(
      onTap: () => Get.to(() => OrderDetailView(code: item['OrderCode'] ?? '')) ,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
                color: Colors.grey.withValues(alpha: 0.05),
                spreadRadius: 0,
                blurRadius: 1,
                offset: Offset(0, 3)
            ),
          ],
        ),
        padding: EdgeInsets.all(10),
        margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  decoration: BoxDecoration(
                      border: Border.all(width: 1, color: statusColor),
                      borderRadius: BorderRadius.circular(20)
                  ),
                  padding: EdgeInsets.fromLTRB(5, 2.5, 5, 2.5),
                  child: CustomText(text: statuses[item['StatusCode']] ?? '', color: statusColor, bold: true, size: 14,),
                ),
                CustomText(text: '🕘${AppFunction.formatDateWithTime(item['CreatedAt'])}')
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                CustomText(text: item['CustomerName'] ?? '', bold: true, size: 18,),
                CustomText(text: item['CustomerPhone'] ?? '')
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          CustomText(text: AppFunction.formatMoney(item['OrderTotalView'] ?? '0')),
                          if(item['PaymentMethod'] != null)
                            AppFunction.getRenderPaymentMethod(item['PaymentMethod'])
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          CustomText(text: '${item['TotalItems']} mặt hàng', color: Colors.blue, size: 13,),
                          if (item['InvoiceId'] != null)
                            CustomText(text: ' - HĐĐT: 345688', color: Colors.blue, size: 13,),
                        ],
                      )
                    ],
                  ),
                ),
                if (statusCode == 'WATTING_PAYMENT')
                  InkWell(
                    onTap:  () => _regenerateQRCode(item['OrderCode']),
                    child: Container(
                      decoration: BoxDecoration(
                          color: AppColors.button,
                          borderRadius: BorderRadius.circular(20)
                      ),
                      padding: EdgeInsets.fromLTRB(10, 5, 10, 5),
                      child: CustomText(text: 'Thanh toán', color: Colors.white, bold: true,),
                    ),
                  ),
                SizedBox(width: 10,),
                InkWell(
                  onTap: () {
                    Get.to(() => OrderDetailView(code: item['OrderCode'] ?? ''));
                  },
                  child: Container(
                    decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(20)
                    ),
                    padding: EdgeInsets.fromLTRB(10, 5, 10, 5),
                    child: CustomText(text: 'Chi tiết', color: Colors.white, bold: true,),
                  ),
                )
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget renderItemOrderCustomer(item) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
              color: Colors.grey.withValues(alpha: 0.05),
              spreadRadius: 0,
              blurRadius: 1,
              offset: Offset(0, 3)
          ),
        ],
      ),
      padding: EdgeInsets.all(10),
      margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Container(
              //   width: 50,
              //   height: 50,
              //   decoration: BoxDecoration(
              //     borderRadius: BorderRadius.circular(180)
              //   ),
              //   margin: EdgeInsets.fromLTRB(0, 0, 5, 0),
              //   clipBehavior: Clip.antiAlias,
              //   child: Image.asset('assets/images/general/avatar.jpg'),
              // ),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(child: CustomText(text: item['CustomerName'] ?? '', bold: true, size: 18,),),
                        CustomText(text: AppFunction.formatMoney(item['SumTotal']), color: Colors.blue,)
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        CustomText(text: '📞${item['CustomerPhone'] ?? ''}'),
                        CustomText(text: 'Tổng đơn ${item['TotalOrder']}')
                      ],
                    )
                  ],
                ),
              )
            ],
          ),
        ],
      ),
    );
  }



  Color getStatusColor(String statusCode) {
    switch(statusCode) {
      case 'WATTING_PAYMENT': // Đang chờ thanh toán
        return Colors.orange;
      case 'CANCEL': // Đã hủy
        return Colors.red;
      case 'SUCCESS': // Thành công
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  Future<void> _regenerateQRCode(String orderCode) async {
    try {
      Get.dialog(
        Center(child: CircularProgressIndicator()),
        barrierDismissible: false,
      );

      // Lấy thông tin đơn hàng để có payment amount
      final orderDetail = await orderController.getDetail(orderCode);
      if (orderDetail != null && orderDetail.isNotEmpty) {
        paymentAmount.value = orderDetail[0]['OrderTotalView'] ?? 0;
      }

      // Gọi API để tạo lại QR code
      final response = await GenerateACBQRUseCase(repository: OrderRepositoryImpl(
        remoteDataSource: OrderRemoteDataSourceImpl(dioClient: Get.find()),
      )).call(orderCode);

      Get.back();

      if (response.result?.qrPay != null) {
        orderCodeForPayment.value = orderCode;
        acbQRCodeData.value = response.result!.qrPay;
        _showQRCodeDialog(context);
      } else {
        Get.snackbar('Lỗi', response.message );
      }
    } catch (e) {
      Get.back();
      Get.snackbar('Lỗi', 'Đã xảy ra lỗi khi tạo lại mã QR: ${e.toString()}');
    }
  }

  Future<void> cancelOrder(String orderCode) async {
    try {
      Get.dialog(
        Center(child: CircularProgressIndicator()),
        barrierDismissible: false,
      );

      print("Request Data: ${{
        "key": "ACB",
        "orderCode": orderCode.toString(),
        "msg": "",
        "statusCode": "CANCEL",
      }}");
      final response = await dioClient.post(
        SmeUrl.updateOrderStatusSME,
        data: {
          "Key": "ACB",
          "OrderCode": orderCode.toString(),
          "Msg": "",
          "StatusCode": "CANCEL",
        },
      );
      print("Response: ${response.data}");
      Get.back();

      if (response.data['Success'] == true) {
        Get.snackbar('Thành công', 'Đã hủy đơn hàng thành công');
        // Refresh data with current filter
        await getData();
      } else {
        Get.snackbar('Lỗi', response.data['Message'] ?? 'Hủy đơn hàng thất bại');
      }
    } on DioException catch (e) {
      print("Dio Error: ${e.message}");
      print("Error Response: ${e.response?.data}");
    } catch (e) {
      print("Unexpected Error: $e");
    }
  }

  void _showManualPaymentConfirmation(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final noteController = TextEditingController();
    final RxString errorText = ''.obs;

    Get.dialog(
      Dialog(
        backgroundColor: AppColors.background,
        insetPadding: EdgeInsets.symmetric(
          horizontal: width * 0.05,
          vertical: width * 0.1,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(width * 0.02),
        ),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: width * 0.03),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(top: width * 0.02),
                        child: Text(
                          "Xác nhận thanh toán thành công",
                          style: PrimaryFont.bold.copyWith(
                            color: AppColors.text,
                            fontSize: width * 0.04,
                          ),
                        ),
                      ),
                      IconButton(
                        icon: Icon(Icons.close, color: AppColors.text, size: width * 0.05),
                        onPressed: () => Get.back(),
                      ),
                    ],
                  ),
                  SizedBox(height: width * 0.01),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: width * 0.02),
                    child: Obx(() => TextField(
                      controller: noteController,
                      decoration: InputDecoration(
                        hintText: 'Nhập ghi chú thanh toán',
                        hintStyle: PrimaryFont.regular.copyWith(
                          color: AppColors.shadow,
                          fontSize: width * 0.03,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(width * 0.01),
                        ),
                        isDense: true,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: width * 0.03,
                          vertical: width * 0.03,
                        ),
                        errorText:
                        errorText.value.isEmpty ? null : errorText.value,
                      ),
                      maxLines: 3,
                      maxLength: 100,
                      style: PrimaryFont.regular.copyWith(
                        color: AppColors.text,
                        fontSize: width * 0.03,
                      ),
                      onChanged: (_) => errorText.value = '',
                    )),
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                      bottom: width * 0.03,
                      right: width * 0.02,
                      left: width * 0.02,
                      top: width * 0.02,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        ElevatedButton(
                          onPressed: () => Get.back(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            minimumSize: Size(width * 0.2, width * 0.08),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(width * 0.02),
                            ),
                          ),
                          child: Text(
                            'Hủy',
                            style: PrimaryFont.bold.copyWith(
                              color: AppColors.background,
                              fontSize: width * 0.03,
                            ),
                          ),
                        ),
                        SizedBox(width: width * 0.02),
                        ElevatedButton(
                          onPressed: () async {
                            if (noteController.text.trim().isEmpty) {
                              errorText.value = 'Vui lòng nhập ghi chú thanh toán';
                              return;
                            }

                            Get.back();

                            Get.dialog(
                              Center(child: CircularProgressIndicator()),
                              barrierDismissible: false,
                            );

                            try {
                              final response = await dioClient.post(
                                SmeUrl.updateOrderStatusSME,
                                data: {
                                  "Key": "ACB",
                                  "OrderCode": orderCodeForPayment.value,
                                  "Msg": "",
                                  "StatusCode": "SUCCESS",
                                },
                              );

                              Get.back();

                              if (response.data['Success'] == true) {
                                paymentSuccess.value = true;
                                dynamic decoded = await getImageBill(orderCodeForPayment.value, UserInfo.branchId);
                                if (decoded != null) {
                                  if (GlobalVar.typePrinter == 'bluetooth') {
                                    bluetoothPrinterController.printBill(decoded);
                                  } else if (GlobalVar.typePrinter == 'usb') {
                                    usbPrinterController.printBill(decoded);
                                  }
                                }
                                // Refresh data
                                Get.snackbar('Thành công', 'Xác nhận thanh toán thành công');
                                Get.back(); // Đóng dialog QR
                                await getData(); // Refresh danh sách đơn hàng
                              } else {
                                Get.snackbar('Lỗi', response.data['message'] ?? 'Xác nhận thanh toán thất bại');
                              }
                            } catch (e) {
                              Get.back();
                              Get.snackbar('Lỗi', 'Đã xảy ra lỗi khi xác nhận thanh toán');
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            minimumSize: Size(width * 0.2, width * 0.08),
                            padding: EdgeInsets.symmetric(horizontal: width * 0.02, vertical: width * 0.015),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(width * 0.02),
                            ),
                          ),
                          child: Text(
                            'Xác nhận',
                            style: PrimaryFont.bold.copyWith(
                              color: AppColors.background,
                              fontSize: width * 0.03,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      barrierDismissible: false,
    );
  }

  void _showQRCodeDialog(BuildContext context) {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '', decimalDigits: 0);

    showGeneralDialog(
        context: context,
        barrierDismissible: false,
        pageBuilder: (context, animation, secondaryAnimation) {
          return Scaffold(
            body: Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.white,
              child: Obx(() {
                if (paymentSuccess.value) {
                  return Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Padding(
                        padding: EdgeInsets.fromLTRB(0, 0, 0, 20),
                        child: CustomText(
                          text: 'Thanh toán thành công',
                          size: 24,
                          bold: true,
                        ),
                      ),
                      Image.asset(
                        'assets/images/self_order/payment_success.png',
                        width: 200,
                      ),
                      Container(
                        width: 150,
                        height: 50,
                        margin: EdgeInsets.fromLTRB(0, 30, 0, 0),
                        child: CustomButton(
                          onTap: () {
                            Get.back();
                            getData();
                          },
                          text: 'Đóng',
                          color: AppColors.primary,
                          textColor: Colors.white,
                        ),
                      ),
                    ],
                  );
                }

                return SingleChildScrollView(
                  padding: EdgeInsets.only(top: 120),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Padding(
                        padding: EdgeInsets.fromLTRB(0, 0, 0, 10),
                        child: CustomText(
                          text: 'Quét mã để thanh toán',
                          size: 24,
                          bold: true,
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Image.asset('assets/images/self_order/acb.png', width: 70),
                          ],
                        ),
                      ),
                      Stack(
                        alignment: Alignment.center,
                        children: [
                          Container(
                            width: 320,
                            height: 320,
                            decoration: BoxDecoration(
                                border: Border.all(color: AppColors.primary, width: 20),
                                borderRadius: BorderRadius.circular(40),
                                color: Colors.white),
                          ),
                          if (acbQRCodeData.value.isNotEmpty)
                            QrImageView(
                              data: acbQRCodeData.value,
                              version: QrVersions.auto,
                              size: 250,
                            ),
                        ],
                      ),
                      Padding(
                          padding: EdgeInsets.fromLTRB(0, 10, 0, 0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomText(
                                text: 'Số tiền: ',
                                italic: true,
                                size: 18,
                              ),
                              CustomText(
                                text: currencyFormat.format(paymentAmount.value),
                                bold: true,
                                italic: true,
                                color: AppColors.primary,
                                size: 18,
                              ),
                            ],
                          )),
                      Padding(
                          padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomText(
                                text: 'Nội dung: ',
                                italic: true,
                                size: 18,
                              ),
                            ],
                          )),
                      Padding(
                          padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomText(
                                text: 'Thanh toán đơn hàng',
                                bold: true,
                                italic: true,
                                color: AppColors.primary,
                                size: 18,
                              ),
                            ],
                          )),
                      Padding(
                          padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomText(
                                text: '#${orderCodeForPayment.value}',
                                bold: true,
                                italic: true,
                                color: AppColors.primary,
                                size: 18,
                              ),
                            ],
                          )),
                      Padding(
                        padding: EdgeInsets.fromLTRB(0, 20, 0, 0),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  width: 100,
                                  margin: EdgeInsets.fromLTRB(0, 0, 0, 0),
                                  child: CustomButton(
                                    onTap: () {
                                      showDialog(
                                          context: context,
                                          builder: (context) {
                                            return AlertDialog(
                                              title: CustomText(text: 'Xác nhận hủy thanh toán?'),
                                              actions: [
                                                Container(
                                                  width: 100,
                                                  margin: EdgeInsets.fromLTRB(0, 0, 5, 0),
                                                  child: CustomButton(
                                                    onTap: () {
                                                      Get.back();
                                                    },
                                                    text: 'Quay lại',
                                                    color: AppColors.shadow,
                                                    textColor: AppColors.background,
                                                  ),
                                                ),
                                                Container(
                                                  width: 100,
                                                  margin: EdgeInsets.fromLTRB(0, 0, 0, 0),
                                                  child: CustomButton(
                                                    onTap: () {
                                                      cancelOrder(orderCodeForPayment.value);
                                                      Get.back();
                                                      Get.back();
                                                    },
                                                    text: 'Xác nhận',
                                                    color: AppColors.primary,
                                                    textColor: AppColors.background,
                                                  ),
                                                ),
                                              ],
                                            );
                                          });
                                    },
                                    text: 'Hủy',
                                    color: AppColors.danger,
                                    textColor: Colors.white,
                                  ),
                                ),
                                Container(
                                  width: 100,
                                  margin: EdgeInsets.fromLTRB(10, 0, 0, 0),
                                  child: CustomButton(
                                    onTap: () {
                                      if (GlobalVar.typePrinter == 'bluetooth') {
                                        bluetoothPrinterController.printQr();
                                      }
                                      else if (GlobalVar.typePrinter == 'usb') {
                                        usbPrinterController.printQr();
                                      }
                                      else {
                                        AppFunction.showError('Máy in chưa được kết nối');
                                      }
                                    },
                                    text: 'In bill',
                                    color: AppColors.primary,
                                    textColor: AppColors.background,
                                  ),
                                ),
                                Container(
                                  width: 100,
                                  margin: EdgeInsets.fromLTRB(10, 0, 0, 0),
                                  child: CustomButton(
                                    onTap: () {
                                      Get.back();
                                    },
                                    text: 'Thanh toán sau',
                                    color: AppColors.primary,
                                    textColor: AppColors.background,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 15),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  width: 320,
                                  margin: EdgeInsets.fromLTRB(0, 0, 0, 0),
                                  child: CustomButton(
                                    onTap: () => _showManualPaymentConfirmation(context),
                                    text: 'confirm_payment'.tr,
                                    color: AppColors.primary,
                                    textColor: AppColors.background,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                );
              }),
            ),
          );
        });
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Đơn Hàng'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        automaticallyImplyLeading: false,

        
      ),
      body: Column(
        children: [
          // Filter widget
          ReusableFilterWidget(
            filterData: filterData,
            statuses: statuses,
            paymentMethods: paymentMethods,
            onFilterChanged: onFilterChanged,
            onClearFilter: onClearFilter,
          ),

          Padding(
            padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
            child: renderSummary(),
          ),
          Expanded(
            child: ListView(
              padding: EdgeInsets.all(10),
              children: [
                if (viewType == 'order')
                for(dynamic item in orders)
                  renderItemOrder(item),
                if (viewType == 'customer')
                for(dynamic item in customerList)
                  renderItemOrderCustomer(item),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
