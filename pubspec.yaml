name: gls_self_order
description: "A new Flutter project."
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.1.5+15

environment:
  sdk: ^3.6.0

dependencies:
  flutter:
    sdk: flutter

  # State Management & Utilities
  get: ^4.7.2

  # Networking & Data Handling
  dio: ^5.7.0
  cached_network_image: ^3.4.1

  # Local Storage
  shared_preferences: ^2.3.3
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.2
  device_info_plus: ^11.5.0

  # Logging & Debugging
  logger: ^2.5.0

  # Internationalization
  intl: ^0.20.2

  # Notifications & Toasts
  fluttertoast: ^8.2.12


  # UI & Design
  cupertino_icons: ^1.0.8
  flutter_localization: ^0.3.2
  carousel_slider: ^5.0.0
  fluentui_system_icons: ^1.1.273
  shimmer: ^3.0.0
  kiosk_mode: ^0.6.0
  flutter_slidable: ^4.0.0
  icons_plus: ^5.0.0
  dropdown_search: ^5.0.6
  qr_flutter: ^4.1.0
  socket_io_client: ^3.1.2
  print_bluetooth_thermal: ^1.1.6
  esc_pos_utils_plus: ^2.0.4
  
  dotted_border: ^3.0.1
  image_picker: ^1.1.2
  flutter_multi_formatter: ^2.13.7
  bot_toast: ^4.1.3
  path: ^1.9.1
  drago_usb_printer: ^0.1.2
  webview_flutter: ^4.13.0
  url_launcher: ^6.3.1
  flutter_launcher_icons: ^0.14.3
  local_auth: ^2.3.0
  smooth_page_indicator: ^1.2.0+3
  flutter_speed_dial: ^7.0.0
  iconsax: ^0.0.8

  # Chart UI
  graphic: ^2.5.1
  fl_chart: ^0.70.2

  # Bloc and report
  flutter_bloc: ^9.0.0
  equatable: ^2.0.7
  get_it: ^8.0.3
  dartz: ^0.10.1
  syncfusion_flutter_charts: ^28.2.11
  flutter_pdfview: ^1.4.0+1
  simple_barcode_scanner: ^0.3.0
  firebase_core: ^3.14.0
  firebase_messaging: ^15.2.7
  flutter_local_notifications: ^19.3.0
  table_calendar: ^3.2.0
  font_awesome_flutter: ^10.8.0
  file_picker: ^10.2.1
  excel: ^4.0.6
  image: ^4.3.0
  
  

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0
  build_runner: ^2.4.7
  hive_generator: ^2.0.1

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/general/logo.jpg"

flutter:

  uses-material-design: true

  assets:
    - assets/images/self_order/
    - assets/images/general/
    - assets/images/fnb/
    - assets/pdfs/terms.pdf

  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
        - asset: assets/fonts/Roboto-Medium.ttf
          weight: 500
        - asset: assets/fonts/Roboto-Regular.ttf
          weight: 400