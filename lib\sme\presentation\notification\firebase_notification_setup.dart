import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/general/views/auth/login_view.dart';
import 'dart:convert';
import '../../../general/controllers/auth_controller.dart';
import '../../../general/controllers/notification_controller.dart';
import '../../../general/views/home_main_view.dart';
import '../../../general/views/order/order_detail_view.dart';
import 'home_notification_manager.dart';
import 'inapp_notificationdialog.dart';

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
FlutterLocalNotificationsPlugin();

final List<Map<String, dynamic>> _pendingNotifications = [];
bool _isAppReady = false;
bool _isInAppNotificationShowing = false;

bool _isAppOpenedFromNotification = false;
bool _isUserCurrentlyInApp = false;
int _currentTabIndex = 0;
@pragma('vm:entry-point')
void notificationTapBackground(NotificationResponse notificationResponse) {
  _isAppOpenedFromNotification = true;

  if (notificationResponse.payload != null) {
    try {
      final Map<String, dynamic> data = jsonDecode(notificationResponse.payload!);

      if (_isAppReady) {
        handleNotificationTap(data);
      } else {
        _pendingNotifications.add(data);
      }
    } catch (e) {
      print('Error parsing notification payload: $e');
    }
  }
}

@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  print("Handling a background message: ${message.messageId}");

  await HomeNotificationManager.savePendingNotification(message);

  if (Get.isRegistered<NotificationController>()) {
    Get.find<NotificationController>().handleNewNotification(message);
  }

  if (message.data['isOnlyHome'] == 'true') {
    await _handleHomeOnlyNotificationInBackground(message);
  }

  if (message.notification != null) {
    print('Background Notification Title: ${message.notification!.title}');
    print('Background Notification Body: ${message.notification!.body}');
  }
}

void markAppAsReady() {
  _isAppReady = true;
  _isUserCurrentlyInApp = true;

  if (_pendingNotifications.isNotEmpty) {
    Future.delayed(Duration(milliseconds: 1500), () {
      for (final data in _pendingNotifications) {
        handleNotificationTap(data);
      }
      _pendingNotifications.clear();
    });
  }
}

void markUserInApp() {
  _isUserCurrentlyInApp = true;
  _isAppOpenedFromNotification = false;
}

Future<void> initializeFirebaseMessaging() async {
  NotificationSettings settings = await FirebaseMessaging.instance.requestPermission(
    alert: true,
    announcement: false,
    badge: true,
    carPlay: false,
    criticalAlert: false,
    provisional: false,
    sound: true,
  );

  print('User granted permission: ${settings.authorizationStatus}');

  String? token = await FirebaseMessaging.instance.getToken();
  print("FCM Token: $token");
  if (token != null) {
    await FirebaseMessaging.instance.subscribeToTopic('user');
  }

  FirebaseMessaging.onMessage.listen((RemoteMessage message) {
    if (message.notification != null) {
      final bool isInApp = message.data['isInApp'] == 'true';
      final bool isOnlyHome = message.data['isOnlyHome'] == 'true';
      _handleNotification(message);
      if(isOnlyHome){
        _handleHomeOnlyNotification(message);
      }else if(isInApp){
        _showInAppNotification(message);
      }else{
        _showLocalNotification(message);
      }

    }
  });

  FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
    _isAppOpenedFromNotification = true;

    if (_isAppReady) {
      Future.delayed(Duration(milliseconds: 500), () {
        handleNotificationTap(message.data);
      });
    } else {
      _pendingNotifications.add(message.data);
    }
  });

  FirebaseMessaging.instance.getInitialMessage().then((RemoteMessage? message) {
    if (message != null) {
      _isAppOpenedFromNotification = true;
      _isUserCurrentlyInApp = false;

      if (Get.isRegistered<NotificationController>()) {
        Get.find<NotificationController>().handleNewNotification(message);
      }

      if (_isAppReady) {
        Future.delayed(Duration(milliseconds: 2000), () {
          handleNotificationTap(message.data);
        });
      } else {
        _pendingNotifications.add(message.data);
      }
    }
  });

  FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

  await _initializeLocalNotifications();
}

Future<void> _initializeLocalNotifications() async {
  const AndroidInitializationSettings initializationSettingsAndroid =
  AndroidInitializationSettings('@mipmap/ic_launcher');

  const DarwinInitializationSettings initializationSettingsDarwin =
  DarwinInitializationSettings(
    requestAlertPermission: false,
    requestBadgePermission: false,
    requestSoundPermission: false,
  );

  const InitializationSettings initializationSettings = InitializationSettings(
    android: initializationSettingsAndroid,
    iOS: initializationSettingsDarwin,
  );

  await flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
    onDidReceiveNotificationResponse: (NotificationResponse response) async {
      print('onDidReceiveNotificationResponse: ${response.payload}');

      _isAppOpenedFromNotification = true;

      if (response.payload != null) {
        try {
          final Map<String, dynamic> data = jsonDecode(response.payload!);

          if (_isAppReady) {
            Future.delayed(Duration(milliseconds: 500), () {
              handleNotificationTap(data);
            });
          } else {
            _pendingNotifications.add(data);
          }
        } catch (e) {
          print('Error parsing notification payload: $e');
          if (_isAppReady) {
            Future.delayed(Duration(milliseconds: 500), () {
              _handleNotificationTapFromString(response.payload!);
            });
          } else {
            _pendingNotifications.add({'_raw_payload': response.payload!});
          }
        }
      }
    },
    onDidReceiveBackgroundNotificationResponse: notificationTapBackground,
  );

  await _createNotificationChannel();
}

Future<void> _createNotificationChannel() async {
  const AndroidNotificationChannel channel = AndroidNotificationChannel(
    'high_importance_channel',
    'High Importance Notifications',
    description: 'This channel is used for important notifications from GLS-SELF-ORDER.',
    importance: Importance.max,
    playSound: true,
  );

  await flutterLocalNotificationsPlugin
      .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
      ?.createNotificationChannel(channel);
}

Future<void> _showLocalNotification(RemoteMessage message) async {
  RemoteNotification? notification = message.notification;
  AndroidNotification? android = message.notification?.android;

  if (notification != null) {
    flutterLocalNotificationsPlugin.show(
      notification.hashCode,
      notification.title,
      notification.body,
      NotificationDetails(
        android: AndroidNotificationDetails(
          'high_importance_channel',
          'High Importance Notifications',
          channelDescription: 'This channel is used for important notifications from GLS-SELF-ORDER.',
          importance: Importance.max,
          priority: Priority.high,
          enableVibration: true,
          playSound: true,
        ),
        iOS: const DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        ),
      ),
      payload: jsonEncode(message.data),
    );
  }
}

void _showInAppNotification(RemoteMessage message) {
  RemoteNotification? notification = message.notification;
  Map<String, dynamic> data = message.data;

  if (notification != null) {
    if (_isInAppNotificationShowing && Get.isDialogOpen == true) {
      Get.back();
    }

    _isInAppNotificationShowing = true;

    Get.dialog(
      InAppNotificationDialog(
        notification: notification,
        data: data,
        onDetailTap: () {
          _isInAppNotificationShowing = false;
          handleNotificationTap(data);
        },
      ),
      barrierDismissible: true,
    ).then((_) {
      _isInAppNotificationShowing = false;
    });
  }
}

void updateCurrentTabIndex(int index) {
  _currentTabIndex = index;
}

Future<bool> _isCurrentlyAtHomePage() async {
  try {
    bool isAtHomeRoute = Get.currentRoute == '/HomeMainView' && _isUserCurrentlyInApp;
    bool isAtHomeTab = _currentTabIndex == 0;

    return isAtHomeRoute && isAtHomeTab;
  } catch (e) {
    return false;
  }
}

Future<void> _handleHomeOnlyNotificationInBackground(RemoteMessage message) async {
  try {
    String notificationId = message.data['notification_id'] ?? message.messageId ?? DateTime.now().millisecondsSinceEpoch.toString();

    bool isDisplayed = await HomeNotificationManager.isNotificationDisplayed(notificationId);
    if (!isDisplayed) {
      await HomeNotificationManager.savePendingNotification(message);
      print('Saved home-only notification to pending (background)');
    }
  } catch (e) {
    print('Error handling home-only notification in background: $e');
  }
}

Future<void> _handleHomeOnlyNotification(RemoteMessage message) async {
  try {
    String notificationId = message.data['notification_id'] ?? message.messageId ?? DateTime.now().millisecondsSinceEpoch.toString();

    bool isDisplayed = await HomeNotificationManager.isNotificationDisplayed(notificationId);
    if (!isDisplayed) {
      bool isCurrentlyAtHome = await _isCurrentlyAtHomePage();

      if (isCurrentlyAtHome) {
        await _showHomeOnlyNotificationDialog(message, notificationId);
        print('Showed home-only notification immediately (at home page)');
      } else {
        await HomeNotificationManager.savePendingNotification(message);
        print('Saved home-only notification to pending (not at home page - tab index: $_currentTabIndex)');
      }
    }
  } catch (e) {
    print('Error handling home-only notification: $e');
  }
}

Future<void> _showHomeOnlyNotificationDialog(RemoteMessage message, String notificationId) async {
  try {
    await HomeNotificationManager.markNotificationAsDisplayed(notificationId);

    if (Get.context != null) {
      Get.dialog(
        InAppNotificationDialog(
          notification: message.notification!,
          data: message.data,
          onDetailTap: () {
            _isInAppNotificationShowing = false;
            handleNotificationTap(message.data);
          },
        ),
        barrierDismissible: true,
      );
    }
  } catch (e) {
    print('Error showing home-only notification dialog: $e');
  }
}

void _handleNotificationAction(String actionUrl) {
  print('Handle notification action: $actionUrl');
}

void _handleNotification(RemoteMessage message) {
  if (Get.isRegistered<NotificationController>()) {
    Get.find<NotificationController>().handleNewNotification(message);
  }

  if (message.notification != null) {
    final bool isInApp = message.data['isInApp'] == 'true';
    final bool isOnlyHome = message.data['isOnlyHome'] == 'true';

    if (isOnlyHome) {
      _handleHomeOnlyNotification(message);
    } else if (isInApp) {
      Get.dialog(
        InAppNotificationDialog(
          notification: message.notification!,
          data: message.data,
          onDetailTap: () {
            handleNotificationTap(message.data);
          },
        ),
      );
    } else {
      _showLocalNotification(message);
    }
  }
}

Future<void> handleNotificationTap(Map<String, dynamic> data) async {
  if (data.containsKey('_raw_payload')) {
    _handleNotificationTapFromString(data['_raw_payload']);
    return;
  }

  try {
    if (!Get.isRegistered<AuthController>()) {
      await Future.delayed(Duration(milliseconds: 1000));
      if (!Get.isRegistered<AuthController>()) {
        Get.offAll(() => HomeMainView());
        return;
      }
    }

    final auth = Get.find<AuthController>();
    final String? route = data['route'];
    final String? id = data['id'];

    if (_isAppOpenedFromNotification && !_isUserCurrentlyInApp) {
      final rememberAccount = await auth.getRememberAccount();

      if (auth.isLoggedIn.value) {
        _handleRouteAfterLogin(route, id);
      } else {
        if (rememberAccount['username'] != '' && rememberAccount['password'] != '') {
          Get.offAll(() => LoginView(autoLogin: true));
          _handleRouteAfterLogin(route, id);
        } else {
          Get.offAll(() => LoginView());
        }
      }

      return;
    }

    if (_isUserCurrentlyInApp && !_isAppOpenedFromNotification) {
      _handleDirectRoute(route, id);
    } else {
      final rememberAccount = await auth.getRememberAccount();
      if (auth.isLoggedIn.value) {
        _handleRouteAfterLogin(route, id);
      } else {
        if (rememberAccount['username'] != '' && rememberAccount['password'] != '') {
          Get.offAll(() => LoginView(autoLogin: true));
          _handleRouteAfterLogin(route, id);
        } else {
          Get.offAll(() => LoginView());
        }
      }

    }

  } catch (e) {
    print('Error in _handleNotificationTap: $e');
    Future.delayed(Duration(milliseconds: 1000), () {
      Get.offAll(() => HomeMainView());
    });
  }
}

void _handleDirectRoute(String? route, String? id) {
  if (route != null) {
    if (route == 'order_details' && id != null) {
      Get.to(() => OrderDetailView(code: id));
    } else if (route == 'settings') {
      Get.offAll(() => HomeMainView(), arguments: {'goTo': 'settings'});
    } else if (route == '/e-menu-sme') {
      if (Get.routeTree.routes.any((route) => route.name == '/e-menu-sme')) {
        Get.toNamed('/e-menu-sme');
      } else {
        Get.offAll(() => HomeMainView());
      }
    } else {
      Get.offAll(() => HomeMainView());
    }
  } else {
    Get.offAll(() => HomeMainView());
  }
}

void _handleRouteAfterLogin(String? route, String? id) {
  if (route != null) {
    if (route == 'order_details' && id != null) {

      Get.dialog(
        Center(child: CircularProgressIndicator()),
        barrierDismissible: false,
      );

      Future.delayed(Duration(milliseconds: 2000), () {
        if (Get.isDialogOpen!) {
          Get.back();
        }

        Get.offAll(() => HomeMainView(), arguments: {'goTo': 'shopping'});
      });

      Future.delayed(Duration(milliseconds: 3000), () {
        if (Get.currentRoute != '/OrderDetailView') {
          Get.to(() => OrderDetailView(code: id));
        }
      });
    } else if (route == 'settings') {
      Future.delayed(Duration(milliseconds: 1000), () {
        Get.offAll(() => HomeMainView(), arguments: {'goTo': 'settings'});
      });
    } else if (route == '/e-menu-sme') {
      if (Get.routeTree.routes.any((route) => route.name == '/e-menu-sme')) {
        Future.delayed(Duration(milliseconds: 1000), () {
          Get.toNamed('/e-menu-sme');
        });
      } else {
        Future.delayed(Duration(milliseconds: 1000), () {
          Get.offAll(() => HomeMainView());
        });
      }
    } else {
      Future.delayed(Duration(milliseconds: 1000), () {
        Get.offAll(() => HomeMainView());
      });
    }
  } else {
    Future.delayed(Duration(milliseconds: 1000), () {
      Get.offAll(() => HomeMainView());
    });
  }
}

void _handleNotificationTapFromString(String payload) {
  final routeMatch = RegExp(r'route:\s*([^,}]+)').firstMatch(payload);
  final idMatch = RegExp(r'id:\s*([^,}]+)').firstMatch(payload);

  if (routeMatch != null) {
    final route = routeMatch.group(1)?.trim();
    final id = idMatch?.group(1)?.trim();

    if (route != null) {
      try {
        if (!Get.isRegistered<AuthController>()) {
          print('AuthController not available for string payload');
          Get.offAll(() => HomeMainView());
          return;
        }

        final auth = Get.find<AuthController>();

        if (_isUserCurrentlyInApp && !_isAppOpenedFromNotification) {
          _handleDirectRoute(route, id);
        } else {
          auth.getRememberAccount().then((rememberAccount) {
            if (rememberAccount['username'] != '' && rememberAccount['password'] != '') {
              Get.offAll(() => LoginView(autoLogin: true));
              _handleRouteAfterLogin(route, id);
            } else {
              Get.offAll(() => LoginView());
            }
          }).catchError((e) {
            Get.offAll(() => HomeMainView());
          });
        }
      } catch (e) {
        Get.offAll(() => HomeMainView());
      }
    }
  }
}