import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/views/account/account_view.dart';
import 'package:gls_self_order/general/views/home_view.dart';
import 'package:gls_self_order/general/views/notify_view.dart';
import 'package:gls_self_order/general/views/order/order_view.dart';
import 'package:gls_self_order/general/views/payment_view.dart';

import '../../sme/presentation/notification/firebase_notification_setup.dart';
import '../../sme/presentation/notification/home_notification_manager.dart';
import '../../sme/presentation/notification/inapp_notificationdialog.dart';

class HomeMainView extends StatefulWidget {
  const HomeMainView({super.key});

  @override
  State<HomeMainView> createState() => _HomeMainViewState();
}

class _HomeMainViewState extends State<HomeMainView> {
  //variable
  final PageStorageBucket bucket = PageStorageBucket();
  Widget currentView = const HomeView();
  List menuItems = [
    {'title': 'Trang chủ', 'icon': Icons.home_outlined, 'view': HomeView()},
    {'title': 'Đơn hàng', 'icon': Icons.shopping_bag_outlined, 'view': OrdersView()},
    {'title': 'Thanh toán', 'icon': Icons.payment, 'view': PaymentView()},
    {'title': 'Thông báo', 'icon': Icons.notifications_none, 'view': NotifyView()},
    {'title': 'Cài đặt', 'icon': Icons.settings, 'view': AccountView(from: 'bottom',)},
    // {'title': 'Marketplace', 'icon': Icons.shopping_cart, 'view': MarketplaceView()},
  ];
  int active = 0;

  @override
  void initState() {
    super.initState();
    updateCurrentTabIndex(0);
    _handleInitialNavigation();
  }

  void _handleInitialNavigation() async {
    final args = Get.arguments;

    bool hasHomeOnlyNotification = await HomeNotificationManager.hasPendingNotifications();

    if (hasHomeOnlyNotification) {
      setState(() {
        active = 0;
        currentView = menuItems[active]['view'];
      });
      Future.delayed(Duration(milliseconds: 500), () {
        _showPendingHomeOnlyNotifications();
      });
    } else {
      if (args != null && args['goTo'] == 'settings') {
        setState(() {
          active = 4;
          currentView = menuItems[active]['view'];
        });
      }
      if (args != null && args['goTo'] == 'shopping') {
        setState(() {
          active = 1;
          currentView = menuItems[active]['view'];
        });
      }
    }

    HomeNotificationManager.cleanupOldNotifications();
  }

  Future<void> _showPendingHomeOnlyNotifications() async {
    if (active != 0) {
      return;
    }
    try {
      List<Map<String, dynamic>> pendingNotifications = await HomeNotificationManager.getPendingNotifications();

      if (pendingNotifications.isNotEmpty) {
        for (Map<String, dynamic> data in pendingNotifications) {
          try {
            await _showHomeOnlyNotificationDialog(data);
            await HomeNotificationManager.markNotificationAsDisplayed(data['id']);
          } catch (e) {
            print('Error showing notification: $e');
          }
        }
      }
    } catch (e) {
      print('Error showing pending home notifications: $e');
    }
  }

  Future<void> _showHomeOnlyNotificationDialog(Map<String, dynamic> data) async {
    RemoteNotification notification = RemoteNotification(
      title: data['title'],
      body: data['body'],
    );

    return Get.dialog(
      InAppNotificationDialog(
        notification: notification,
        data: data['extra_data'],
        onDetailTap: () {

        },
      ),
      barrierDismissible: true,
    );
  }


  void _onTabChanged(int newIndex) {
    currentView = menuItems[newIndex]['view'];
    active = newIndex;
    setState(() {});

    updateCurrentTabIndex(newIndex);

    if (newIndex == 0) {
      Future.delayed(Duration(milliseconds: 300), () {
        _checkAndShowHomeNotifications();
      });
    }
  }

  Future<void> _checkAndShowHomeNotifications() async {
    if (active == 0) {
      bool hasPending = await HomeNotificationManager.hasPendingNotifications();
      if (hasPending) {
        _showPendingHomeOnlyNotifications();
      }
    }
  }




  Widget renderMenu() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (int index = 0; index < menuItems.length; index++)
          InkWell(
            onTap: () {
              currentView = menuItems[index]['view'];
              active = index;
              setState(() {
                _onTabChanged(index);
              });
            },
            child: Padding(
              padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Icon(menuItems[index]['icon'], color: active == index ? AppColors.primary : AppColors.shadow),
                  Text(menuItems[index]['title'], style: TextStyle(color: active == index ? AppColors.primary : AppColors.shadow, fontSize: 13))
                ],
              ),
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) {
          return;
        }

        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: CustomText(text: 'Xác nhận', size: 20,),
              content: CustomText(text: 'Bạn có chắc muốn thoát ứng dụng?'),
              actions: <Widget>[
                TextButton(
                  onPressed: () {
                    Get.back();
                  },
                  child: CustomText(text: 'Ở lại'),
                ),
                TextButton(
                  onPressed: () {
                    exit(0);
                  },
                  child: CustomText(text: 'Thoát'),
                ),
              ],
            );
          },
        );
      },
      child: Scaffold(
        body: PageStorage(
          bucket: bucket,
          child: currentView,
        ),
        bottomNavigationBar: BottomAppBar(
          color: Colors.white,
          shadowColor: Colors.grey,
          padding: EdgeInsets.fromLTRB(2, 0, 2, 0),
          child: renderMenu(),
        ),
      ),
    );
  }
}
