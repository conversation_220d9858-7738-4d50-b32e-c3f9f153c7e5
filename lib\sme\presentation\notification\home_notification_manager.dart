import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

class HomeNotificationManager {

  static const String _homeOnlyNotificationsKey = 'home_only_notifications';
  static const String _storageNotificationsKey = 'storage_notifications';
  static const String _displayedNotificationsKey = 'displayed_home_notifications';

  static Future<void> savePendingNotification(RemoteMessage message) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();

      final bool isOnlyHome = message.data['isOnlyHome'] == 'true';

      if (isOnlyHome) {
        await _saveHomeOnlyNotification(message, prefs);
      }

      await _saveToStorageNotifications(message, prefs);

    } catch (e) {
      print('Error saving notification: $e');
    }
  }

  static Future<void> _saveHomeOnlyNotification(RemoteMessage message, SharedPreferences prefs) async {
    try {
      List<String> homeOnlyNotifications = prefs.getStringList(_homeOnlyNotificationsKey) ?? [];

      String notificationId = message.data['notification_id'] ??
          message.messageId ??
          DateTime.now().millisecondsSinceEpoch.toString();

      bool alreadyExists = homeOnlyNotifications.any((notification) {
        try {
          Map<String, dynamic> data = jsonDecode(notification);
          return data['id'] == notificationId;
        } catch (e) {
          return false;
        }
      });

      if (!alreadyExists) {
        Map<String, dynamic> notificationData = {
          'id': notificationId,
          'title': message.notification?.title ?? '',
          'body': message.notification?.body ?? '',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          'action_url': message.data['action_url'],
          'route': message.data['route'],
          'extra_data': message.data,
          'isRead': false,
          'isOnlyHome': true,
        };

        homeOnlyNotifications.add(jsonEncode(notificationData));
        await prefs.setStringList(_homeOnlyNotificationsKey, homeOnlyNotifications);

        print('Saved home-only notification: ${notificationData['title']}');
      }
    } catch (e) {
      print('Error saving home-only notification: $e');
    }
  }

  static Future<void> _saveToStorageNotifications(RemoteMessage message, SharedPreferences prefs) async {
    try {
      List<String> storageNotifications = prefs.getStringList(_storageNotificationsKey) ?? [];

      String notificationId = message.data['notification_id'] ??
          message.messageId ??
          DateTime.now().millisecondsSinceEpoch.toString();

      bool alreadyExists = storageNotifications.any((notification) {
        try {
          Map<String, dynamic> data = jsonDecode(notification);
          return data['id'] == notificationId;
        } catch (e) {
          return false;
        }
      });

      if (!alreadyExists) {
        Map<String, dynamic> notificationData = {
          'id': notificationId,
          'title': message.notification?.title ?? '',
          'body': message.notification?.body ?? '',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          'action_url': message.data['action_url'],
          'route': message.data['route'],
          'extra_data': message.data,
          'isRead': false,
          'type': _getNotificationType(message.data),
          'isOnlyHome': message.data['isOnlyHome'] == 'true',
        };

        storageNotifications.add(jsonEncode(notificationData));
        await prefs.setStringList(_storageNotificationsKey, storageNotifications);

        print('Saved storage notification: ${notificationData['title']}');
      }
    } catch (e) {
      print('Error saving storage notification: $e');
    }
  }

  static String _getNotificationType(Map<String, dynamic> data) {

    if (data.containsKey('type')) {
      return data['type'];
    }

    final route = data['route'];
    final actionUrl = data['action_url'];

    if (route == 'order_details' || actionUrl?.contains('order') == true) {
      return 'system';
    } else if (actionUrl?.contains('promotion') == true || actionUrl?.contains('discount') == true) {
      return 'promotion';
    }

    return 'system';
  }

  static Future<List<Map<String, dynamic>>> getPendingNotifications() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      List<String>? homeOnlyNotifications = prefs.getStringList(_homeOnlyNotificationsKey);

      if (homeOnlyNotifications == null || homeOnlyNotifications.isEmpty) {
        return [];
      }

      List<Map<String, dynamic>> notifications = [];
      for (String notificationJson in homeOnlyNotifications) {
        try {
          Map<String, dynamic> data = jsonDecode(notificationJson);
          notifications.add(data);
        } catch (e) {
          print('Error parsing home-only notification: $e');
        }
      }

      notifications.sort((a, b) => (a['timestamp'] ?? 0).compareTo(b['timestamp'] ?? 0));

      return notifications;
    } catch (e) {
      print('Error getting pending notifications: $e');
      return [];
    }
  }

  static Future<List<Map<String, dynamic>>> getStorageNotifications() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      List<String>? storageNotifications = prefs.getStringList(_storageNotificationsKey);

      if (storageNotifications == null || storageNotifications.isEmpty) {
        return [];
      }

      List<Map<String, dynamic>> notifications = [];
      for (String notificationJson in storageNotifications) {
        try {
          Map<String, dynamic> data = jsonDecode(notificationJson);
          notifications.add(data);
        } catch (e) {
          print('Error parsing storage notification: $e');
        }
      }

      notifications.sort((a, b) => (b['timestamp'] ?? 0).compareTo(a['timestamp'] ?? 0));

      return notifications;
    } catch (e) {
      print('Error getting storage notifications: $e');
      return [];
    }
  }

  static Future<void> markNotificationAsRead(String notificationId) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();

      await _updateNotificationInStorage(_storageNotificationsKey, notificationId, {'isRead': true}, prefs);

      await _updateNotificationInStorage(_homeOnlyNotificationsKey, notificationId, {'isRead': true}, prefs);

      print('Marked notification as read: $notificationId');
    } catch (e) {
      print('Error marking notification as read: $e');
    }
  }

  static Future<void> _updateNotificationInStorage(String key, String notificationId, Map<String, dynamic> updates, SharedPreferences prefs) async {
    try {
      List<String> notifications = prefs.getStringList(key) ?? [];
      List<String> updatedNotifications = [];

      for (var notificationJson in notifications) {
        try {
          Map<String, dynamic> data = jsonDecode(notificationJson);
          if (data['id'] == notificationId) {
            data.addAll(updates);
          }
          updatedNotifications.add(jsonEncode(data));
        } catch (e) {
          updatedNotifications.add(notificationJson);
        }
      }

      await prefs.setStringList(key, updatedNotifications);
    } catch (e) {
      print('Error updating notification in storage: $e');
    }
  }

  static Future<void> clearPendingNotifications() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.remove(_homeOnlyNotificationsKey);
      print('Cleared all pending home-only notifications');
    } catch (e) {
      print('Error clearing pending notifications: $e');
    }
  }

  static Future<void> clearStorageNotifications() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.remove(_storageNotificationsKey);
      print('Cleared all storage notifications');
    } catch (e) {
      print('Error clearing storage notifications: $e');
    }
  }

  static Future<void> clearAllNotifications() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.remove(_homeOnlyNotificationsKey);
      await prefs.remove(_storageNotificationsKey);
      print('Cleared all notifications');
    } catch (e) {
      print('Error clearing all notifications: $e');
    }
  }

  static Future<void> removePendingNotification(String notificationId) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();

      List<String> homeOnlyNotifications = prefs.getStringList(_homeOnlyNotificationsKey) ?? [];
      homeOnlyNotifications.removeWhere((notification) {
        try {
          Map<String, dynamic> data = jsonDecode(notification);
          return data['id'] == notificationId;
        } catch (e) {
          return false;
        }
      });
      await prefs.setStringList(_homeOnlyNotificationsKey, homeOnlyNotifications);

      print('Removed pending notification: $notificationId');
    } catch (e) {
      print('Error removing pending notification: $e');
    }
  }

  static Future<bool> isNotificationDisplayed(String notificationId) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      List<String> displayedNotifications = prefs.getStringList(_displayedNotificationsKey) ?? [];
      return displayedNotifications.contains(notificationId);
    } catch (e) {
      print('Error checking notification display status: $e');
      return false;
    }
  }

  static Future<void> markNotificationAsDisplayed(String notificationId) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      List<String> displayedNotifications = prefs.getStringList(_displayedNotificationsKey) ?? [];

      if (!displayedNotifications.contains(notificationId)) {
        displayedNotifications.add(notificationId);
        await prefs.setStringList(_displayedNotificationsKey, displayedNotifications);
        print('Marked notification as displayed: $notificationId');
      }

      await removePendingNotification(notificationId);
    } catch (e) {
      print('Error marking notification as displayed: $e');
    }
  }

  static Future<bool> hasPendingNotifications() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      List<String>? homeOnlyNotifications = prefs.getStringList(_homeOnlyNotificationsKey);
      return homeOnlyNotifications != null && homeOnlyNotifications.isNotEmpty;
    } catch (e) {
      print('Error checking pending notifications: $e');
      return false;
    }
  }

  static Future<int> getPendingNotificationsCount() async {
    try {
      List<Map<String, dynamic>> notifications = await getPendingNotifications();
      return notifications.length;
    } catch (e) {
      print('Error getting pending notifications count: $e');
      return 0;
    }
  }

  static Future<void> cleanupOldNotifications() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();

      List<String> displayedNotifications = prefs.getStringList(_displayedNotificationsKey) ?? [];
      if (displayedNotifications.length > 1000) {
        displayedNotifications = displayedNotifications.sublist(displayedNotifications.length - 1000);
        await prefs.setStringList(_displayedNotificationsKey, displayedNotifications);
        print('Cleaned up old displayed notifications');
      }

      await _cleanupOldNotificationsInStorage(_homeOnlyNotificationsKey, prefs);

      await _cleanupOldNotificationsInStorage(_storageNotificationsKey, prefs, daysToKeep: 30);

    } catch (e) {
      print('Error cleaning up old notifications: $e');
    }
  }

  static Future<void> _cleanupOldNotificationsInStorage(String key, SharedPreferences prefs, {int daysToKeep = 7}) async {
    try {
      List<String> notifications = prefs.getStringList(key) ?? [];
      int cutoffTime = DateTime.now().subtract(Duration(days: daysToKeep)).millisecondsSinceEpoch;

      notifications.removeWhere((notification) {
        try {
          Map<String, dynamic> data = jsonDecode(notification);
          int timestamp = data['timestamp'] ?? 0;
          return timestamp < cutoffTime;
        } catch (e) {
          return true; // Xóa notification lỗi format
        }
      });

      await prefs.setStringList(key, notifications);
      print('Cleaned up old notifications in $key');
    } catch (e) {
      print('Error cleaning up old notifications in $key: $e');
    }
  }
}